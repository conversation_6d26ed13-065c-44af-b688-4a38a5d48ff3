<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Elastic.Apm.NetCoreAll" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
    <PackageReference Include="WeBuyCars.Core" />
    <PackageReference Include="WeBuyCars.Core.Infrastructure.ActiveDirectory.Authentication" />
    <PackageReference Include="WeBuyCars.Core.Infrastructure.DynamicLinq" />
    <PackageReference Include="WeBuyCars.Core.Infrastructure.Errors" />
    <PackageReference Include="WeBuyCars.Core.Infrastructure.Scalar" />
    <PackageReference Include="WeBuyCars.Core.Infrastructure.Storage" />
    <PackageReference Include="WeBuyCars.Core.Infrastructure.Swagger" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\WeBuyCars.My.Garage.Core\WeBuyCars.My.Garage.Core.csproj" />
    <ProjectReference Include="..\WeBuyCars.My.Garage.Data.SQL\WeBuyCars.My.Garage.Data.SQL.csproj" />
    <ProjectReference Include="..\WeBuyCars.My.Garage.Infrastructure.IMS\WeBuyCars.My.Garage.Infrastructure.IMS.csproj" />
  </ItemGroup>

</Project>
