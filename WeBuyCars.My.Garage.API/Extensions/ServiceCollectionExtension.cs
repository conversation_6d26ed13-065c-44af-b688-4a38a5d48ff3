using System.Reflection;
using System.Text.Json.Serialization;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using WeBuyCars.Core.Exceptions;
using WeBuyCars.Core.Infrastructure.ActiveDirectory.Authentication.Extensions;
using WeBuyCars.Core.Infrastructure.Errors.Middleware;
using WeBuyCars.Core.Infrastructure.Scalar.Extensions;
using WeBuyCars.Core.Infrastructure.Swagger.Extensions;
using WeBuyCars.My.Garage.Data.SQL.Context;
using WeBuyCars.My.Garage.Infrastructure.IMS.Extensions;
using WeBuyCars.My.Garage.Infrastructure.RTMC.Extensions;

namespace WeBuyCars.My.Garage.API.Extensions;

public static class ServiceCollectionExtension
{
    public static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        ConfigureMvc(services);
        ConfigureDbContext(services, configuration);
        ConfigureAuthentication(services, configuration);
        ConfigureAuthorization(services, configuration);
        ConfigureHealthChecks(services);
        ConfigureFluentValidation(services);
        ConfigureServices(services);
        ConfigureRepositories(services);
        ConfigureIMSIntegration(services, configuration);
        ConfigureRTMCIntegration(services, configuration);

        services.AddSwaggerDocumentation()
            .AddResponseCompression()
            .AddResponseCaching()
            .AddHttpContextAccessor()
            .AddAllElasticApm()
            .AddScalarDocumentation()
            .AddOptions();
    }

    public static void Configure(IApplicationBuilder app)
    {
        app.UseMiddleware<ExceptionMiddleware>();
        app.UseHttpsRedirection();
        app.UseSwaggerDocumentation();
        app.UseRouting();
        app.UseAuthentication();
        app.UseAuthorization();
        app.UseScalarDocumentation();

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
            endpoints.MapHealthChecks("/health", new HealthCheckOptions
            {
                AllowCachingResponses = false
            });
        });
    }

    private static void ConfigureMvc(IServiceCollection services)
    {
        services.AddMvc(options => { options.EnableEndpointRouting = false; })
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
                options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                options.JsonSerializerOptions.WriteIndented = true;
                options.JsonSerializerOptions.PropertyNamingPolicy = null;
            })
            .AddNewtonsoftJson(o =>
            {
                o.SerializerSettings.Formatting = Formatting.Indented;
                o.SerializerSettings.Converters.Add(new StringEnumConverter());
                o.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                o.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
                o.SerializerSettings.DateParseHandling = DateParseHandling.DateTimeOffset;
                o.SerializerSettings.ContractResolver = new DefaultContractResolver
                {
                    NamingStrategy = new DefaultNamingStrategy()
                };
            });

        services.AddMvcCore().AddApiExplorer();
    }

    private static void ConfigureHealthChecks(IServiceCollection services)
    {
        services.AddHealthChecks();
    }

    private static void ConfigureAuthentication(IServiceCollection services, IConfiguration configuration)
    {
        services.AddAuthenticationSchemes(configuration);
        Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = false;
    }

    private static void ConfigureAuthorization(IServiceCollection services, IConfiguration configuration)
    {
        services.AddControllers(config =>
        {
            var policy = new AuthorizationPolicyBuilder().RequireAuthenticatedUser().Build();
            config.Filters.Add(new AuthorizeFilter(policy));
        });

        services.AddAuthorization(options =>
        {
            options.ConfigureAuthorization(configuration);

            var policies = new (string Name, string[] Claims)[]
            {
                // Administrators can perform all actions.
                ("IsAdmin", ["mygarage_admin"]),
                // Creators can only perform create/post actions.
                ("CanCreate", ["mygarage_admin", "mygarage_create"]),
                // Readers can only perform read/get actions.
                ("CanRead", ["mygarage_admin", "mygarage_read"])
            };

            foreach (var (name, claims) in policies)
            {
                options.AddPolicy(name, policy =>
                    policy.RequireAssertion(context =>
                        claims.Any(claim => context.User.HasClaim(c => c.Type == claim || c.Value == claim))
                    )
                );
            }
        });
    }

    private static void ConfigureDbContext(IServiceCollection services, IConfiguration configuration)
    {
        services.AddDbContext<MyGarageContext>((_, options) =>
        {
            options.UseSqlServer(configuration.GetConnectionString("WBCDB01") + "Application Name=MyGarage;",
                sqlOptions =>
                {
                    sqlOptions.MigrationsAssembly("WeBuyCars.My.Garage.Infrastructure.Data.SQL");
                    sqlOptions.MigrationsHistoryTable("__EFMigrationsHistory", MyGarageContext.DefaultSchema);
                });
            options.ConfigureWarnings(warnings => warnings.Ignore(RelationalEventId.PendingModelChangesWarning));
        });
    }

    private static void ConfigureFluentValidation(IServiceCollection services)
    {
        ValidatorOptions.Global.DefaultRuleLevelCascadeMode = CascadeMode.Stop;
    }

    private static void ConfigureServices(IServiceCollection services)
    {
        // services.AddScoped<IMessageService, MessageService>();
    }

    private static void ConfigureRepositories(IServiceCollection services)
    {
        // services.AddScoped<ILeadRepository, LeadRepository>();
    }

    private static void ConfigureIMSIntegration(IServiceCollection services, IConfiguration configuration)
    {
        services.AddIMSIntegration(configuration);
    }
    
    private static void ConfigureRTMCIntegration(IServiceCollection services, IConfiguration configuration)
    {
        services.AddEnatisRTMCIntegration(configuration);
    }
}