using System.ComponentModel;
using System.Runtime.Serialization;

namespace WeBuyCars.My.Garage.Core.Enums;

/// <summary>
/// RTMC Document Type
/// </summary>
public enum DocumentTypeEnum
{
    [Description("Unknown")]
    [EnumMember(Value = "00")]
    Unknown = 00,

    [Description("Reg no certificate")]
    [EnumMember(Value = "01")]
    RegNoCertificate = 01,

    [Description("RSA ID document")]
    [EnumMember(Value = "02")]
    RSAIDDocument = 02,

    [Description("Foreign ID document")]
    [EnumMember(Value = "03")]
    ForeignIDDocument = 03,

    [Description("Business reg certificate")]
    [EnumMember(Value = "04")]
    BusinessRegCertificate = 04,

    [Description("Foreign passport")]
    [EnumMember(Value = "05")]
    ForeignPassport = 05,

    [Description("ID-Card")]
    [EnumMember(Value = "06")]
    IDCard = 06,

    [Description("Travel document")]
    [EnumMember(Value = "07")]
    TravelDocument = 07,

    [Description("Foreign driver's licence")]
    [EnumMember(Value = "08")]
    Foreigndriverslicence = 08,

    [Description("SA driver's licence")]
    [EnumMember(Value = "09")]
    SAdriverslicence = 09,

    [Description("SA passport")]
    [EnumMember(Value = "10")]
    SAPassport = 10,

    [Description("Reference book")]
    [EnumMember(Value = "11")]
    ReferenceBook = 11,

    [Description("Transvaal reg no")]
    [EnumMember(Value = "12")]
    TransvaalRegNo = 12,

    [Description("Merged alias")]
    [EnumMember(Value = "13")]
    MergedAlias = 13,

    [Description("Pseudo person")]
    [EnumMember(Value = "97")]
    PseudoPperson = 97,

    [Description("Other")]
    [EnumMember(Value = "98")]
    Other = 98,

    [Description("None")]
    [EnumMember(Value = "99")]
    None = 99
}