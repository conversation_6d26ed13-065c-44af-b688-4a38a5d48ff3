using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.My.Garage.Core.Entities;

namespace WeBuyCars.My.Garage.Infrastructure.Data.SQL.EntityConfigurations;

public class VehicleConfiguration : IEntityTypeConfiguration<Vehicle>
{
    public void Configure(EntityTypeBuilder<Vehicle> builder)
{
    builder.ToTable(VehicleConstants.TableName);

    builder.HasKey(x => x.Id);
    builder.Property(x => x.Id)
        .IsRequired()
        .ValueGeneratedOnAdd();

    builder.Property(x => x.UserAccountNumber)
        .IsRequired()
        .HasMaxLength(50);

    builder.HasIndex(x => x.UserAccountNumber)
        .HasDatabaseName("IX_SoldVehicleImsData_UserAccountNumber");

    builder.Property(x => x.Category)
        .IsRequired()
        .HasConversion<string>()
        .HasMaxLength(20);

    builder.Property(x => x.Make)
        .HasMaxLength(VehicleConstants.MaxMakeLength);

    builder.Property(x => x.Model)
        .HasMaxLength(VehicleConstants.MaxModelLength);

    builder.Property(x => x.Variant)
        .HasMaxLength(VehicleConstants.MaxVariantLength);

    builder.Property(x => x.Colour)
        .HasMaxLength(VehicleConstants.MaxColourLength);

    builder.Property(x => x.VIN)
        .HasMaxLength(VehicleConstants.MaxVINLength);

    builder.Property(x => x.EngineNumber)
        .HasMaxLength(VehicleConstants.MaxEngineNumberLength);

    builder.Property(x => x.LicencePlate)
        .HasMaxLength(VehicleConstants.MaxLicencePlateLength);

    builder.Property(x => x.RegisterNumber)
        .HasMaxLength(VehicleConstants.MaxRegisterNumberLength);

    builder.Property(x => x.MMCode)
        .HasMaxLength(VehicleConstants.MaxMMCodeLength);

    builder.Property(x => x.ServiceHistory)
        .HasMaxLength(VehicleConstants.MaxServiceHistoryLength);

    builder.Property(x => x.FuelType)
        .HasMaxLength(VehicleConstants.MaxFuelTypeLength);

    builder.Property(x => x.Transmission)
        .HasMaxLength(VehicleConstants.MaxTransmissionLength);

    builder.Property(x => x.TyreSize)
        .HasMaxLength(VehicleConstants.MaxTyreSizeLength);

    builder.Property(x => x.RimSize)
        .HasMaxLength(VehicleConstants.MaxRimSizeLength);

    builder.Property(x => x.TyreProfile)
        .HasMaxLength(VehicleConstants.MaxTyreProfileLength);

    builder.Property(x => x.LastServiceKm)
        .HasPrecision(18, 2);

    builder.Property(x => x.CreatedAt)
        .IsRequired();

    builder.Property(x => x.UpdatedAt)
        .IsRequired();
}
}


public class VehicleConstants
{
    public const string TableName = "Vehicle";
    
    public const int MaxMakeLength = 100;
    public const int MaxModelLength = 100;
    public const int MaxVariantLength = 100;
    public const int MaxColourLength = 50;
    public const int MaxVINLength = 50; // Standard VIN length
    public const int MaxEngineNumberLength = 50;
    public const int MaxLicencePlateLength = 30;
    public const int MaxRegisterNumberLength = 50;
    public const int MaxMMCodeLength = 30;
    public const int MaxServiceHistoryLength = 1000;
    public const int MaxFuelTypeLength = 50;
    public const int MaxTransmissionLength = 50;
    public const int MaxTyreSizeLength = 50;
    public const int MaxRimSizeLength = 50;
    public const int MaxTyreProfileLength = 50;
}