
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WeBuyCars.My.Garage.API", "WeBuyCars.My.Garage.API\WeBuyCars.My.Garage.API.csproj", "{6D29796F-5092-4D4D-8E2C-48EF646D8968}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WeBuyCars.My.Garage.Core", "WeBuyCars.My.Garage.Core\WeBuyCars.My.Garage.Core.csproj", "{F5799D64-A5C9-4197-B417-72178912917A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WeBuyCars.My.Garage.Infrastructure.Data.SQL", "WeBuyCars.My.Garage.Infrastructure.Data.SQL\WeBuyCars.My.Garage.Infrastructure.Data.SQL.csproj", "{B5F30B0A-72A3-49CB-8D35-8AEBD46A0089}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WeBuyCars.My.Garage.Infrastructure.IMS", "WeBuyCars.My.Garage.Infrastructure.IMS\WeBuyCars.My.Garage.Infrastructure.IMS.csproj", "{3F518267-C412-4A34-A8C4-543DD6B7EFC8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WeBuyCars.My.Garage.Infrastructure.RTMC", "WeBuyCars.My.Garage.Infrastructure.RTMC\WeBuyCars.My.Garage.Infrastructure.RTMC.csproj", "{356C11E4-4BB2-4B57-A727-D45248413159}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6D29796F-5092-4D4D-8E2C-48EF646D8968}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6D29796F-5092-4D4D-8E2C-48EF646D8968}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6D29796F-5092-4D4D-8E2C-48EF646D8968}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6D29796F-5092-4D4D-8E2C-48EF646D8968}.Debug|x64.Build.0 = Debug|Any CPU
		{6D29796F-5092-4D4D-8E2C-48EF646D8968}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6D29796F-5092-4D4D-8E2C-48EF646D8968}.Debug|x86.Build.0 = Debug|Any CPU
		{6D29796F-5092-4D4D-8E2C-48EF646D8968}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6D29796F-5092-4D4D-8E2C-48EF646D8968}.Release|Any CPU.Build.0 = Release|Any CPU
		{6D29796F-5092-4D4D-8E2C-48EF646D8968}.Release|x64.ActiveCfg = Release|Any CPU
		{6D29796F-5092-4D4D-8E2C-48EF646D8968}.Release|x64.Build.0 = Release|Any CPU
		{6D29796F-5092-4D4D-8E2C-48EF646D8968}.Release|x86.ActiveCfg = Release|Any CPU
		{6D29796F-5092-4D4D-8E2C-48EF646D8968}.Release|x86.Build.0 = Release|Any CPU
		{F5799D64-A5C9-4197-B417-72178912917A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F5799D64-A5C9-4197-B417-72178912917A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F5799D64-A5C9-4197-B417-72178912917A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F5799D64-A5C9-4197-B417-72178912917A}.Debug|x64.Build.0 = Debug|Any CPU
		{F5799D64-A5C9-4197-B417-72178912917A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F5799D64-A5C9-4197-B417-72178912917A}.Debug|x86.Build.0 = Debug|Any CPU
		{F5799D64-A5C9-4197-B417-72178912917A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F5799D64-A5C9-4197-B417-72178912917A}.Release|Any CPU.Build.0 = Release|Any CPU
		{F5799D64-A5C9-4197-B417-72178912917A}.Release|x64.ActiveCfg = Release|Any CPU
		{F5799D64-A5C9-4197-B417-72178912917A}.Release|x64.Build.0 = Release|Any CPU
		{F5799D64-A5C9-4197-B417-72178912917A}.Release|x86.ActiveCfg = Release|Any CPU
		{F5799D64-A5C9-4197-B417-72178912917A}.Release|x86.Build.0 = Release|Any CPU
		{B5F30B0A-72A3-49CB-8D35-8AEBD46A0089}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B5F30B0A-72A3-49CB-8D35-8AEBD46A0089}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B5F30B0A-72A3-49CB-8D35-8AEBD46A0089}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B5F30B0A-72A3-49CB-8D35-8AEBD46A0089}.Debug|x64.Build.0 = Debug|Any CPU
		{B5F30B0A-72A3-49CB-8D35-8AEBD46A0089}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B5F30B0A-72A3-49CB-8D35-8AEBD46A0089}.Debug|x86.Build.0 = Debug|Any CPU
		{B5F30B0A-72A3-49CB-8D35-8AEBD46A0089}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B5F30B0A-72A3-49CB-8D35-8AEBD46A0089}.Release|Any CPU.Build.0 = Release|Any CPU
		{B5F30B0A-72A3-49CB-8D35-8AEBD46A0089}.Release|x64.ActiveCfg = Release|Any CPU
		{B5F30B0A-72A3-49CB-8D35-8AEBD46A0089}.Release|x64.Build.0 = Release|Any CPU
		{B5F30B0A-72A3-49CB-8D35-8AEBD46A0089}.Release|x86.ActiveCfg = Release|Any CPU
		{B5F30B0A-72A3-49CB-8D35-8AEBD46A0089}.Release|x86.Build.0 = Release|Any CPU
		{3F518267-C412-4A34-A8C4-543DD6B7EFC8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3F518267-C412-4A34-A8C4-543DD6B7EFC8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3F518267-C412-4A34-A8C4-543DD6B7EFC8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3F518267-C412-4A34-A8C4-543DD6B7EFC8}.Debug|x64.Build.0 = Debug|Any CPU
		{3F518267-C412-4A34-A8C4-543DD6B7EFC8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{3F518267-C412-4A34-A8C4-543DD6B7EFC8}.Debug|x86.Build.0 = Debug|Any CPU
		{3F518267-C412-4A34-A8C4-543DD6B7EFC8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3F518267-C412-4A34-A8C4-543DD6B7EFC8}.Release|Any CPU.Build.0 = Release|Any CPU
		{3F518267-C412-4A34-A8C4-543DD6B7EFC8}.Release|x64.ActiveCfg = Release|Any CPU
		{3F518267-C412-4A34-A8C4-543DD6B7EFC8}.Release|x64.Build.0 = Release|Any CPU
		{3F518267-C412-4A34-A8C4-543DD6B7EFC8}.Release|x86.ActiveCfg = Release|Any CPU
		{3F518267-C412-4A34-A8C4-543DD6B7EFC8}.Release|x86.Build.0 = Release|Any CPU
		{356C11E4-4BB2-4B57-A727-D45248413159}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{356C11E4-4BB2-4B57-A727-D45248413159}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{356C11E4-4BB2-4B57-A727-D45248413159}.Debug|x64.ActiveCfg = Debug|Any CPU
		{356C11E4-4BB2-4B57-A727-D45248413159}.Debug|x64.Build.0 = Debug|Any CPU
		{356C11E4-4BB2-4B57-A727-D45248413159}.Debug|x86.ActiveCfg = Debug|Any CPU
		{356C11E4-4BB2-4B57-A727-D45248413159}.Debug|x86.Build.0 = Debug|Any CPU
		{356C11E4-4BB2-4B57-A727-D45248413159}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{356C11E4-4BB2-4B57-A727-D45248413159}.Release|Any CPU.Build.0 = Release|Any CPU
		{356C11E4-4BB2-4B57-A727-D45248413159}.Release|x64.ActiveCfg = Release|Any CPU
		{356C11E4-4BB2-4B57-A727-D45248413159}.Release|x64.Build.0 = Release|Any CPU
		{356C11E4-4BB2-4B57-A727-D45248413159}.Release|x86.ActiveCfg = Release|Any CPU
		{356C11E4-4BB2-4B57-A727-D45248413159}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
