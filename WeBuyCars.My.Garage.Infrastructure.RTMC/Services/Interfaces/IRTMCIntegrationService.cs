using Refit;
using WeBuyCars.My.Garage.Infrastructure.RTMC.Models;

namespace WeBuyCars.My.Garage.Infrastructure.RTMC.Services.Interfaces;

public interface IRTMCIntegrationService
{
    [Post("/v1/RTMC/GetVehicle")] // OR GetVehicleDetailedInformation?
    Task<VehicleDetailResponse> GetVehicle(VehicleDetailRequest request, CancellationToken cancellationToken);
    
    [Post("/v1/RTMC/GetOwnerTitleHolderConfirmation")]
    Task<VehicleOwnerVerificationResponse> GetOwnerTitleHolderConfirmation(VehicleOwnerVerificationRequest reques, CancellationToken cancellationToken);
}