namespace WeBuyCars.My.Garage.Infrastructure.RTMC.Models;

public class VehicleDetailResponse
{
    // TODO: Reduce the number of properties to only the ones required

    public string AxlesDriven { get; set; }
    public string AxlesTotal { get; set; }
    public string CategoryCode { get; set; }
    public string CategoryDescription { get; set; }
    public string DescriptionCode { get; set; }
    public string DescriptionDescription { get; set; }
    public string DrivenCode { get; set; }
    public string DrivenDescription { get; set; }
    public string EngineDisplacement { get; set; }
    public string EngineNumber { get; set; }
    public string FuelTypeCode { get; set; }
    public string FuelTypeDescription { get; set; }
    public string GVM { get; set; }
    public string LicenceChangeDate { get; set; }
    public string LicenceExpiryDate { get; set; }
    public string LicenceNumber { get; set; }
    public string LifeStatusCode { get; set; }
    public string LifeStatusDescription { get; set; }
    public string MainColourCode { get; set; }
    public string MainColourDescription { get; set; }
    public string MakeCode { get; set; }
    public string MakeDescription { get; set; }
    public string ModelNameCode { get; set; }
    public string ModelNameDescription { get; set; }
    public string NetPower { get; set; }
    public string NoOfWheels { get; set; }
    public string OverallHeight { get; set; }
    public string OverallLength { get; set; }
    public string OverallWidth { get; set; }
    public string PrePreviousLicenceNumber { get; set; }
    public string PreviousLicenceNumber { get; set; }
    public string RegisterNumber { get; set; }
    public string RegistrationTypeCode { get; set; }
    public string RegistrationTypeDescription { get; set; }
    public string VehicleCertificateNumber { get; set; }
    public string RoadworthyStatusCode { get; set; }
    public string RoadworthyStatusDescription { get; set; }
    public string RoadworthyStatusDate { get; set; }
    public string RoadworthyTestDate { get; set; }
    public string SapClearanceDate { get; set; }
    public string SapClearanceStatusCode { get; set; }
    public string SapClearanceStatusDescription { get; set; }
    public string SapMarkCode { get; set; }
    public string SapMarkDescription { get; set; }
    public string SapMarkDate { get; set; }
    public string Tare { get; set; }
    public string VehicleStateCode { get; set; }
    public string VehicleStateDescription { get; set; }
    public string VehicleStateDate { get; set; }
    public string VinOrChassis { get; set; }


    //Vehicle Detail Additional Information
    public string LicenceLiabilityDate { get; set; }
    public string RegAuthorityOfLicensingCode { get; set; }
    public string RegAuthorityOfLicensingName { get; set; }
    public string RegAuthorityOfLicenceNumberCode { get; set; }
    public string RegAuthorityOfLicenceNumberName { get; set; }
    public string RegistrationDate { get; set; }
    public string RegistrationQualifierCode { get; set; }
    public string RegistrationQualifierDescription { get; set; }
    public string RegistrationQualifierDate { get; set; }
    public string DataOwnerCode { get; set; }
    public string DataOwnerDescription { get; set; }
    public string Timestamp { get; set; }
    public string TransmissionCode { get; set; }
    public string TransmissionDescription { get; set; }
    public string FirstLicensingDate { get; set; }
    public string ModelNumber { get; set; }
    public string VehicleUsageDescription { get; set; }
    public string PreviousVehicleCertificateNumber { get; set; }
    public string FirstRegistrationDate { get; set; }
    public string RegistrationAllowed { get; set; }
}