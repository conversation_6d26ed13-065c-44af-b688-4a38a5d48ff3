using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using WeBuyCars.Core.Infrastructure.ActiveDirectory.Authentication.Extensions;
using WeBuyCars.My.Garage.Infrastructure.RTMC.Configuration;
using WeBuyCars.My.Garage.Infrastructure.RTMC.Services;
using WeBuyCars.My.Garage.Infrastructure.RTMC.Services.Interfaces;

namespace WeBuyCars.My.Garage.Infrastructure.RTMC.Extensions;

public static class RTMCServiceExtensions
{
    /// <summary>
    /// Registers the Ims API integration services in the Microsoft.Extensions.DependencyInjection.IServiceCollection.
    /// </summary>
    /// <param name="services">The Microsoft.Extensions.DependencyInjection.IServiceCollection to add services to.</param>
    /// <param name="configuration">Application configuration properties.</param>
    /// <returns>The same service collection so that multiple calls can be chained.</returns>
    public static IServiceCollection AddEnatisRTMCIntegration(this IServiceCollection services, IConfiguration configuration)
    {
        services.ConfigureApiIntegrationWithRefit<RTMCServiceOptions, IRTMCIntegrationService, RTMCAzureSettings>("enatis-rtmc-api", configuration, true);
        services.AddScoped<IRTMCService, RTMCService>();
        return services;
    }
}