<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <noWarn>1591</noWarn>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\WeBuyCars.My.Garage.Core\WeBuyCars.My.Garage.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Refit" />
    <PackageReference Include="WeBuyCars.Core.Infrastructure.ActiveDirectory.Authentication" />
    <PackageReference Include="WeBuyCars.Core.Infrastructure.Authentication" />
  </ItemGroup>

</Project>
